---
title: A2A and MCP
description: How A2A integrates with Model Context Protocol
---

# A2A and MCP

The Model Context Protocol (MCP) provides a standardized way for AI models to access external tools and data sources. A2A extends this concept to enable direct agent-to-agent communication.

## MCP Foundation

MCP establishes:
- Standardized tool interfaces
- Context sharing mechanisms
- Resource access protocols
- Security boundaries

## A2A Extensions

A2A builds upon MCP by adding:
- Agent discovery mechanisms
- Inter-agent message routing
- Distributed context management
- Cross-agent tool sharing

## Integration Benefits

Combining A2A with MCP provides:
- **Unified Protocol**: Single standard for all agent interactions
- **Tool Sharing**: Agents can share capabilities seamlessly
- **Context Continuity**: Maintain context across agent boundaries
- **Scalable Architecture**: Support for large-scale agent networks

## Implementation Patterns

Common integration patterns:
- MCP servers as agent endpoints
- Shared tool registries
- Context propagation chains
- Federated authentication
