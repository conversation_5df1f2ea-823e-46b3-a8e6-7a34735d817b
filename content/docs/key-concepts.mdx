---
title: Key Concepts
description: Essential concepts for understanding A2A communication
---

# Key Concepts

Understanding these fundamental concepts is crucial for implementing effective A2A communication systems.

## Agent Identity

Each agent in an A2A system must have:
- Unique identifier
- Capability description
- Communication protocols
- Authentication credentials

## Message Protocols

A2A systems rely on standardized message formats:
- **Request/Response**: Synchronous communication
- **Publish/Subscribe**: Asynchronous event-driven communication
- **Streaming**: Continuous data flow between agents

## Coordination Patterns

Common patterns for agent coordination:
- **Master/Worker**: Central coordinator distributes tasks
- **Peer-to-Peer**: Agents communicate directly
- **Pipeline**: Sequential processing through multiple agents
- **Broadcast**: One-to-many communication

## State Management

Agents must manage:
- Local state
- Shared state
- State synchronization
- Conflict resolution
