@import "tailwindcss";
@import "fumadocs-ui/css/neutral.css";
@import "fumadocs-ui/css/preset.css";

:root {
  --color-fd-background: oklch(0.98 0.01 240);
  --color-fd-foreground: oklch(0.25 0.01 240);
  --color-fd-card: oklch(0.99 0.005 240);
  --color-fd-card-foreground: oklch(0.25 0.01 240);
  --color-fd-popover: oklch(0.99 0.005 240);
  --color-fd-popover-foreground: oklch(0.25 0.01 240);
  --color-fd-primary: oklch(0.55 0.18 230);
  --color-fd-primary-foreground: oklch(0.98 0.005 240);
  --color-fd-secondary: oklch(0.96 0.03 240);
  --color-fd-secondary-foreground: oklch(0.3 0.05 230);
  --color-fd-muted: oklch(0.95 0.02 240);
  --color-fd-muted-foreground: oklch(0.5 0.03 240);
  --color-fd-accent: oklch(0.96 0.03 240);
  --color-fd-accent-foreground: oklch(0.3 0.05 230);
  --color-fd-destructive: oklch(0.55 0.18 30);
  --color-fd-border: oklch(0.9 0.02 240);
  --color-fd-input: oklch(0.85 0.02 240);
  --color-fd-ring: oklch(0.55 0.15 230);
  --color-fd-radius: 0.5rem;
  --color-fd-sidebar: oklch(0.97 0.015 240);
  --color-fd-sidebar-foreground: oklch(0.25 0.01 240);
  --color-fd-sidebar-primary: oklch(0.55 0.18 230);
  --color-fd-sidebar-primary-foreground: oklch(0.98 0.005 240);
  --color-fd-sidebar-accent: oklch(0.96 0.03 240);
  --color-fd-sidebar-accent-foreground: oklch(0.3 0.05 230);
  --color-fd-sidebar-border: oklch(0.88 0.02 240);
  --color-fd-sidebar-ring: oklch(0.55 0.15 230);
  --color-fd-chart-1: oklch(0.55 0.18 230);
  --color-fd-chart-2: oklch(0.65 0.15 180);
  --color-fd-chart-3: oklch(0.6 0.16 270);
  --color-fd-chart-4: oklch(0.7 0.14 130);
  --color-fd-chart-5: oklch(0.5 0.17 30);
}

.dark {
  --color-fd-background: oklch(0.2 0.01 240);
  --color-fd-foreground: oklch(0.9 0.01 240);
  --color-fd-card: oklch(0.22 0.015 240);
  --color-fd-card-foreground: oklch(0.9 0.01 240);
  --color-fd-popover: oklch(0.22 0.015 240);
  --color-fd-popover-foreground: oklch(0.9 0.01 240);
  --color-fd-primary: oklch(0.65 0.15 230);
  --color-fd-primary-foreground: oklch(0.15 0.01 240);
  --color-fd-secondary: oklch(0.27 0.03 240);
  --color-fd-secondary-foreground: oklch(0.85 0.03 230);
  --color-fd-muted: oklch(0.25 0.02 240);
  --color-fd-muted-foreground: oklch(0.7 0.03 240);
  --color-fd-accent: oklch(0.27 0.03 240);
  --color-fd-accent-foreground: oklch(0.85 0.03 230);
  --color-fd-destructive: oklch(0.65 0.15 30);
  --color-fd-border: oklch(0.3 0.02 240);
  --color-fd-input: oklch(0.35 0.02 240);
  --color-fd-ring: oklch(0.65 0.12 230);
  --color-fd-sidebar: oklch(0.18 0.015 240);
  --color-fd-sidebar-foreground: oklch(0.9 0.01 240);
  --color-fd-sidebar-primary: oklch(0.65 0.15 230);
  --color-fd-sidebar-primary-foreground: oklch(0.15 0.01 240);
  --color-fd-sidebar-accent: oklch(0.27 0.03 240);
  --color-fd-sidebar-accent-foreground: oklch(0.85 0.03 230);
  --color-fd-sidebar-border: oklch(0.28 0.02 240);
  --color-fd-sidebar-ring: oklch(0.65 0.12 230);
  --color-fd-chart-1: oklch(0.65 0.15 230);
  --color-fd-chart-2: oklch(0.7 0.12 180);
  --color-fd-chart-3: oklch(0.68 0.13 270);
  --color-fd-chart-4: oklch(0.75 0.11 130);
  --color-fd-chart-5: oklch(0.6 0.14 30);
}
